# frozen_string_literal: true

# Email Content Management Controller
# Handles email campaign content creation, editing, and management
class EmailContentController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign
  before_action :set_email_campaign, only: [:show, :edit, :update, :destroy, :preview]

  # GET /campaigns/:campaign_id/email_content
  def show
    @email_campaign ||= @campaign.build_email_campaign(default_email_attributes)
    @preview_data = {
      subject_line: @email_campaign.subject_line,
      preview_text: @email_campaign.preview_text,
      content: @email_campaign.content,
      from_name: @email_campaign.from_name,
      from_email: @email_campaign.from_email
    }
  end

  # GET /campaigns/:campaign_id/email_content/new
  def new
    @email_campaign = @campaign.build_email_campaign(default_email_attributes)
  end

  # GET /campaigns/:campaign_id/email_content/edit
  def edit
    # @email_campaign is set by before_action
  end

  # POST /campaigns/:campaign_id/email_content
  def create
    @email_campaign = @campaign.build_email_campaign(email_campaign_params)

    if @email_campaign.save
      flash[:notice] = 'Email content created successfully!'
      redirect_to campaign_email_content_path(@campaign)
    else
      flash.now[:alert] = 'Failed to create email content.'
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaigns/:campaign_id/email_content
  def update
    if @email_campaign.update(email_campaign_params)
      flash[:notice] = 'Email content updated successfully!'
      redirect_to campaign_email_content_path(@campaign)
    else
      flash.now[:alert] = 'Failed to update email content.'
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaigns/:campaign_id/email_content
  def destroy
    @email_campaign.destroy
    flash[:notice] = 'Email content deleted successfully!'
    redirect_to campaign_path(@campaign)
  end

  # GET /campaigns/:campaign_id/email_content/preview
  def preview
    render json: {
      subject_line: @email_campaign.subject_line,
      preview_text: @email_campaign.preview_text,
      content: @email_campaign.content,
      from_name: @email_campaign.from_name,
      from_email: @email_campaign.from_email,
      estimated_send_time: @email_campaign.estimated_send_time
    }
  end

  # POST /campaigns/:campaign_id/email_content/generate_ai_content
  def generate_ai_content
    service = EmailSpecialistAgentService.new(campaign: @campaign)
    result = service.generate_campaign_content(
      brand_voice: params[:brand_voice] || 'professional',
      email_type: params[:email_type] || 'promotional',
      key_message: params[:key_message]
    )

    if result[:status] == 'success'
      flash[:notice] = 'AI email content generated successfully!'
    else
      flash[:alert] = "Failed to generate AI content: #{result[:message]}"
    end

    redirect_to campaign_email_content_path(@campaign)
  end

  # POST /campaigns/:campaign_id/email_content/optimize_content
  def optimize_content
    service = EmailSpecialistAgentService.new(campaign: @campaign)
    result = service.optimize_campaign_content(
      optimization_goals: params[:optimization_goals]&.split(',')&.map(&:strip) || ['engagement'],
      target_metrics: params[:target_metrics]
    )

    if result[:status] == 'success'
      flash[:notice] = 'Email content optimized successfully!'
    else
      flash[:alert] = "Failed to optimize content: #{result[:message]}"
    end

    redirect_to campaign_email_content_path(@campaign)
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
  end

  def set_email_campaign
    @email_campaign = @campaign.email_campaign
    redirect_to new_campaign_email_content_path(@campaign) unless @email_campaign
  end

  def email_campaign_params
    params.require(:email_campaign).permit(
      :subject_line, :preview_text, :content, :from_name, :from_email,
      settings: {}
    )
  end

  def default_email_attributes
    {
      subject_line: "#{@campaign.name} - Email Campaign",
      content: "Email content for #{@campaign.name}",
      from_name: current_user.full_name || current_tenant.name,
      from_email: current_user.email
    }
  end
end
