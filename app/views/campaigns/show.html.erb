<!-- Modern Campaign Show Page -->
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Enhanced Header Section -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
        <div class="flex-1">
          <!-- Campaign Title and Status -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 mb-6">
            <div class="flex items-center space-x-3 mb-3 sm:mb-0">
              <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
                <%= render 'shared/icons/heroicon', name: 'chart-bar', variant: 'solid', css_class: 'w-8 h-8 text-white' %>
              </div>
              <h1 class="text-3xl font-bold text-gray-900"><%= @campaign.name %></h1>
            </div>
            <%= render 'dashboard/status_badge',
                status: @campaign.status,
                text: @campaign.status.titleize,
                variant: 'soft',
                size: 'md' %>
          </div>

          <!-- Campaign Description -->
          <% if @campaign.description.present? %>
            <p class="text-gray-600 text-lg mb-6 leading-relaxed"><%= @campaign.description %></p>
          <% end %>

          <!-- Campaign Meta Information -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
              <%= render 'shared/icons/heroicon', name: 'tag', css_class: 'w-5 h-5 text-blue-500' %>
              <div>
                <p class="text-sm text-gray-500">Campaign Type</p>
                <p class="font-semibold text-gray-900"><%= @campaign.campaign_type.titleize %></p>
              </div>
            </div>

            <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
              <%= render 'shared/icons/heroicon', name: 'currency-dollar', css_class: 'w-5 h-5 text-green-500' %>
              <div>
                <p class="text-sm text-gray-500">Budget</p>
                <p class="font-semibold text-gray-900">$<%= number_with_delimiter(@campaign.budget_in_dollars.round) %></p>
              </div>
            </div>

            <% if @campaign.start_date %>
              <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
                <%= render 'shared/icons/heroicon', name: 'calendar', css_class: 'w-5 h-5 text-purple-500' %>
                <div>
                  <p class="text-sm text-gray-500">Duration</p>
                  <p class="font-semibold text-gray-900">
                    <%= @campaign.start_date.strftime("%b %d") %>
                    <% if @campaign.end_date %>
                      - <%= @campaign.end_date.strftime("%b %d") %>
                    <% end %>
                  </p>
                </div>
              </div>
            <% end %>

            <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
              <%= render 'shared/icons/heroicon', name: 'users', css_class: 'w-5 h-5 text-orange-500' %>
              <div>
                <p class="text-sm text-gray-500">Target Audience</p>
                <p class="font-semibold text-gray-900"><%= truncate(@campaign.target_audience, length: 20) %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 lg:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
          <%= link_to edit_campaign_path(@campaign),
              class: "inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" do %>
            <%= render 'shared/icons/heroicon', name: 'pencil', css_class: 'w-5 h-5 mr-2' %>
            Edit Campaign
          <% end %>

          <%= link_to campaigns_path,
              class: "inline-flex items-center justify-center px-6 py-3 bg-white text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md" do %>
            <%= render 'shared/icons/heroicon', name: 'arrow-left', css_class: 'w-5 h-5 mr-2' %>
            Back to Campaigns
          <% end %>
        </div>
      </div>
    </div>

    <!-- Key Metrics Dashboard -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
      <!-- Performance Metrics -->
      <% if @performance_summary.present? && @performance_summary.any? %>
        <%= render 'dashboard/metric_card',
            title: 'Total Impressions',
            value: number_with_delimiter(@performance_summary[:total_impressions] || 0),
            icon: 'eye',
            color: 'blue',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Total Clicks',
            value: number_with_delimiter(@performance_summary[:total_clicks] || 0),
            icon: 'cursor-arrow-rays',
            color: 'green',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Conversions',
            value: number_with_delimiter(@performance_summary[:total_conversions] || 0),
            icon: 'chart-bar-square',
            color: 'purple',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Revenue',
            value: "$#{number_with_precision(@performance_summary[:total_revenue] || 0, precision: 0, delimiter: ',')}",
            icon: 'currency-dollar',
            color: 'emerald',
            trend: nil %>
      <% else %>
        <%= render 'dashboard/metric_card',
            title: 'Impressions',
            value: '0',
            icon: 'eye',
            color: 'blue',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Clicks',
            value: '0',
            icon: 'cursor-arrow-rays',
            color: 'green',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Conversions',
            value: '0',
            icon: 'chart-bar-square',
            color: 'purple',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Revenue',
            value: '$0',
            icon: 'currency-dollar',
            color: 'emerald',
            trend: nil %>
      <% end %>
    </div>

    <!-- Campaign Progress Section -->
    <% if @campaign.start_date && @campaign.end_date %>
      <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <%= render 'shared/icons/heroicon', name: 'clock', css_class: 'w-6 h-6 text-blue-500' %>
            <h2 class="text-xl font-semibold text-gray-900">Campaign Progress</h2>
          </div>
          <div class="text-right">
            <div class="text-3xl font-bold text-blue-600"><%= @campaign.progress_percentage %>%</div>
            <div class="text-sm text-gray-500">Complete</div>
          </div>
        </div>

        <%= render 'dashboard/progress_bar',
            value: @campaign.progress_percentage,
            max_value: 100,
            color: 'primary',
            size: 'lg',
            show_percentage: true %>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-sm text-gray-500 mb-1">Start Date</div>
            <div class="font-semibold text-gray-900"><%= @campaign.start_date.strftime("%b %d, %Y") %></div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-sm text-gray-500 mb-1">End Date</div>
            <div class="font-semibold text-gray-900"><%= @campaign.end_date.strftime("%b %d, %Y") %></div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-sm text-gray-500 mb-1">Duration</div>
            <div class="font-semibold text-gray-900">
              <% if @campaign.duration_in_days %>
                <%= @campaign.duration_in_days %> days
              <% else %>
                N/A
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Main Content Layout -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">

      <!-- Primary Content Area -->
      <div class="xl:col-span-2 space-y-8">

        <!-- Campaign Type Specific Content -->
        <% case @campaign.campaign_type %>
        <% when 'email' %>
          <div class="bg-white rounded-xl shadow-sm p-8">
            <div class="flex items-center justify-between mb-8">
              <div class="flex items-center space-x-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                  <%= render 'shared/icons/heroicon', name: 'envelope', class: 'w-6 h-6 text-blue-600' %>
                </div>
                <div>
                  <h2 class="text-2xl font-semibold text-gray-900">Email Campaign</h2>
                  <p class="text-gray-500">Email marketing configuration and content</p>
                </div>
              </div>
              <% if @campaign.email_campaign %>
                <%= render 'dashboard/status_badge',
                    status: 'configured',
                    text: 'Configured',
                    variant: 'soft',
                    size: 'sm' %>
              <% else %>
                <%= render 'dashboard/status_badge',
                    status: 'setup_required',
                    text: 'Setup Required',
                    variant: 'soft',
                    size: 'sm' %>
              <% end %>
            </div>

            <% if @campaign.email_campaign %>
              <div class="space-y-6">
                <!-- Email Details Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                      <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 font-medium"><%= @campaign.email_campaign.subject_line %></p>
                      </div>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">From Information</label>
                      <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900"><%= @campaign.email_campaign.from_name %></p>
                        <p class="text-gray-500 text-sm"><%= @campaign.email_campaign.from_email %></p>
                      </div>
                    </div>

                    <% if @campaign.email_campaign.preview_text.present? %>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Preview Text</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                          <p class="text-gray-900"><%= @campaign.email_campaign.preview_text %></p>
                        </div>
                      </div>
                    <% end %>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content Preview</label>
                    <div class="bg-gray-50 rounded-lg p-4 h-48 overflow-y-auto">
                      <p class="text-gray-900 text-sm leading-relaxed"><%= @campaign.email_campaign.preview_snippet(300) %></p>
                    </div>
                  </div>
                </div>

                <!-- Email Statistics -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Email Statistics</h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-blue-600"><%= number_with_delimiter(@campaign.email_campaign.recipient_count) %></div>
                      <div class="text-sm text-gray-600">Recipients</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-indigo-600"><%= @campaign.email_campaign.estimated_send_time %> min</div>
                      <div class="text-sm text-gray-600">Est. Send Time</div>
                    </div>
                  </div>
                </div>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <%= render 'shared/icons/heroicon', name: 'envelope', class: 'w-10 h-10 text-gray-400' %>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Email Content Not Set Up</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">Configure your email content, subject line, and recipient list to start sending campaigns.</p>
                <button class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md">
                  <%= render 'shared/icons/heroicon', name: 'plus', class: 'w-5 h-5 mr-2' %>
                  Setup Email Content
                </button>
              </div>
            <% end %>
          </div>

        <% when 'social' %>
          <div class="bg-white rounded-xl shadow-sm p-8">
            <div class="flex items-center justify-between mb-8">
              <div class="flex items-center space-x-4">
                <div class="p-3 bg-green-100 rounded-lg">
                  <%= render 'shared/icons/heroicon', name: 'chat-bubble-left-right', class: 'w-6 h-6 text-green-600' %>
                </div>
                <div>
                  <h2 class="text-2xl font-semibold text-gray-900">Social Media Campaign</h2>
                  <p class="text-gray-500">Social media content and platform management</p>
                </div>
              </div>
              <% if @campaign.social_campaign %>
                <%= render 'dashboard/status_badge',
                    status: 'configured',
                    text: 'Configured',
                    variant: 'soft',
                    size: 'sm' %>
              <% else %>
                <%= render 'dashboard/status_badge',
                    status: 'setup_required',
                    text: 'Setup Required',
                    variant: 'soft',
                    size: 'sm' %>
              <% end %>
            </div>

        <% if @campaign.social_campaign %>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Platforms</label>
              <div class="flex flex-wrap gap-2">
                <% @campaign.social_campaign.platforms.each do |platform| %>
                  <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800">
                    <%= platform.titleize %>
                  </span>
                <% end %>
              </div>
            </div>

            <% if @campaign.social_campaign.hashtags.present? %>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Hashtags</label>
                <div class="flex flex-wrap gap-2">
                  <% @campaign.social_campaign.hashtag_list.each do |hashtag| %>
                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800">
                      <%= hashtag.start_with?('#') ? hashtag : "##{hashtag}" %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Content Variants</label>
              <div class="space-y-3">
                <% @campaign.social_campaign.content_variants.each do |platform, content| %>
                  <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-900"><%= platform.titleize %></span>
                      <span class="text-xs text-gray-500"><%= content.length %> characters</span>
                    </div>
                    <p class="text-sm text-gray-700"><%= truncate(content, length: 150) %></p>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <span class="text-sm text-gray-500">Est. Reach</span>
                <p class="text-lg font-semibold text-gray-900"><%= number_with_delimiter(@campaign.social_campaign.estimated_reach) %></p>
              </div>
              <div>
                <span class="text-sm text-gray-500">Scheduled Posts</span>
                <p class="text-lg font-semibold text-gray-900"><%= @campaign.social_campaign.scheduled_posts.count %></p>
              </div>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Social Content Not Set Up</h3>
            <p class="text-gray-600 mb-4">Configure your social media content and platforms.</p>
            <button class="inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Setup Social Content
            </button>
          </div>
        <% end %>
      </div>

    <% when 'seo' %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900 flex items-center">
            <svg class="w-6 h-6 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            SEO Campaign Details
          </h2>
          <% if @campaign.seo_campaign %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              Configured
            </span>
          <% else %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
              Setup Required
            </span>
          <% end %>
        </div>

        <% if @campaign.seo_campaign %>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Target Keywords</label>
              <div class="flex flex-wrap gap-2">
                <% @campaign.seo_campaign.keyword_list.each do |keyword| %>
                  <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-purple-100 text-purple-800">
                    <%= keyword %>
                  </span>
                <% end %>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
              <p class="text-gray-900 bg-gray-50 rounded-lg p-3"><%= @campaign.seo_campaign.meta_title %></p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
              <p class="text-gray-900 bg-gray-50 rounded-lg p-3"><%= @campaign.seo_campaign.meta_description %></p>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <span class="text-sm text-gray-500">Optimization Score</span>
                <p class="text-lg font-semibold text-gray-900"><%= @campaign.seo_campaign.optimization_score %>%</p>
              </div>
              <div>
                <span class="text-sm text-gray-500">Est. Traffic Increase</span>
                <p class="text-lg font-semibold text-gray-900">+<%= number_with_delimiter(@campaign.seo_campaign.estimated_traffic_increase) %></p>
              </div>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">SEO Strategy Not Set Up</h3>
            <p class="text-gray-600 mb-4">Configure your SEO strategy and target keywords.</p>
            <button class="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Setup SEO Strategy
            </button>
          </div>
        <% end %>
      </div>

    <% when 'multi_channel' %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900 flex items-center">
            <svg class="w-6 h-6 mr-3 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Multi-Channel Campaign
          </h2>
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
            Advanced
          </span>
        </div>

        <div class="space-y-4">
          <p class="text-gray-600">This campaign combines multiple marketing channels for maximum impact.</p>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Email Component -->
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div class="flex items-center mb-2">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="font-medium text-blue-900">Email</span>
              </div>
              <% if @campaign.email_campaign %>
                <span class="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">Configured</span>
              <% else %>
                <span class="text-xs text-gray-700 bg-gray-100 px-2 py-1 rounded">Not Set Up</span>
              <% end %>
            </div>

            <!-- Social Component -->
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
              <div class="flex items-center mb-2">
                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                </svg>
                <span class="font-medium text-green-900">Social</span>
              </div>
              <% if @campaign.social_campaign %>
                <span class="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">Configured</span>
              <% else %>
                <span class="text-xs text-gray-700 bg-gray-100 px-2 py-1 rounded">Not Set Up</span>
              <% end %>
            </div>

            <!-- SEO Component -->
            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <div class="flex items-center mb-2">
                <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="font-medium text-purple-900">SEO</span>
              </div>
              <% if @campaign.seo_campaign %>
                <span class="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">Configured</span>
              <% else %>
                <span class="text-xs text-gray-700 bg-gray-100 px-2 py-1 rounded">Not Set Up</span>
              <% end %>
            </div>
          </div>

          <div class="text-center pt-4">
            <button class="inline-flex items-center px-4 py-2 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Configure Multi-Channel Strategy
            </button>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Sidebar -->
  <div class="space-y-8">

    <!-- Campaign Stats -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Statistics</h3>

      <% if @performance_summary.present? && @performance_summary.any? %>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Total Impressions</span>
            <span class="font-semibold text-gray-900"><%= number_with_delimiter(@performance_summary[:total_impressions]) %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Total Clicks</span>
            <span class="font-semibold text-gray-900"><%= number_with_delimiter(@performance_summary[:total_clicks]) %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Conversions</span>
            <span class="font-semibold text-gray-900"><%= number_with_delimiter(@performance_summary[:total_conversions]) %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Revenue</span>
            <span class="font-semibold text-green-600">$<%= number_with_precision(@performance_summary[:total_revenue], precision: 2, delimiter: ',') %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Cost</span>
            <span class="font-semibold text-red-600">$<%= number_with_precision(@performance_summary[:total_cost], precision: 2, delimiter: ',') %></span>
          </div>

          <hr class="border-gray-200">

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Click Rate</span>
            <span class="font-semibold text-blue-600"><%= @performance_summary[:average_ctr] %>%</span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Conversion Rate</span>
            <span class="font-semibold text-purple-600"><%= @performance_summary[:average_conversion_rate] %>%</span>
          </div>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 mb-2">No Performance Data</h4>
          <p class="text-gray-600 text-sm">Campaign metrics will appear here once the campaign is active and generating data.</p>
        </div>
      <% end %>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

      <div class="space-y-3">
        <% if @campaign.can_be_activated? %>
          <%= link_to activate_campaign_path(@campaign), method: :patch,
              class: "w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors",
              data: { confirm: "Are you sure you want to activate this campaign?" } do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"></path>
            </svg>
            Activate Campaign
          <% end %>
        <% elsif @campaign.active? %>
          <%= link_to pause_campaign_path(@campaign), method: :patch,
              class: "w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 text-white font-medium rounded-lg hover:bg-yellow-700 transition-colors",
              data: { confirm: "Are you sure you want to pause this campaign?" } do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Pause Campaign
          <% end %>
        <% end %>

        <%= link_to edit_campaign_path(@campaign),
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Campaign
        <% end %>

        <%= link_to campaigns_path,
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Campaigns
        <% end %>
      </div>
    </div>

    <!-- Campaign Timeline -->
    <% if @campaign.start_date || @campaign.end_date %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Timeline</h3>

        <div class="space-y-4">
          <% if @campaign.start_date %>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
              <div>
                <p class="text-sm font-medium text-gray-900">Campaign Start</p>
                <p class="text-xs text-gray-500"><%= @campaign.start_date.strftime("%B %d, %Y") %></p>
              </div>
            </div>
          <% end %>

          <% if @campaign.start_date && @campaign.end_date %>
            <% mid_date = @campaign.start_date + (@campaign.duration_in_days / 2).days %>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
              <div>
                <p class="text-sm font-medium text-gray-900">Mid-Campaign Review</p>
                <p class="text-xs text-gray-500"><%= mid_date.strftime("%B %d, %Y") %></p>
              </div>
            </div>
          <% end %>

          <% if @campaign.end_date %>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
              <div>
                <p class="text-sm font-medium text-gray-900">Campaign End</p>
                <p class="text-xs text-gray-500"><%= @campaign.end_date.strftime("%B %d, %Y") %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
