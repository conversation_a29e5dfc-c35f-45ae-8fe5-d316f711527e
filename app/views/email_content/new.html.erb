<% content_for :title, "Create Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_path(@campaign), 
              class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900">Create Email Content</h1>
          <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            <%= @campaign.campaign_type.humanize %>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-sm border">
      <div class="p-6">
        <div class="mb-6">
          <h2 class="text-lg font-medium text-gray-900">Email Campaign Setup</h2>
          <p class="text-sm text-gray-600 mt-1">Create compelling email content for your campaign</p>
        </div>

        <%= form_with model: [@campaign, @email_campaign], local: true, class: "space-y-6" do |form| %>
          <% if @email_campaign.errors.any? %>
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                  <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                    <% @email_campaign.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          <% end %>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :subject_line, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :subject_line, 
                  class: "form-input",
                  placeholder: "Enter your email subject line...",
                  maxlength: 150 %>
              <p class="text-xs text-gray-500 mt-1">Maximum 150 characters</p>
            </div>

            <div>
              <%= form.label :preview_text, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :preview_text, 
                  class: "form-input",
                  placeholder: "Preview text (optional)...",
                  maxlength: 200 %>
              <p class="text-xs text-gray-500 mt-1">Maximum 200 characters</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :from_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :from_name, 
                  class: "form-input",
                  placeholder: "Sender name..." %>
            </div>

            <div>
              <%= form.label :from_email, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.email_field :from_email, 
                  class: "form-input",
                  placeholder: "<EMAIL>" %>
            </div>
          </div>

          <div>
            <%= form.label :content, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :content, 
                class: "form-textarea",
                rows: 12,
                placeholder: "Write your email content here..." %>
            <p class="text-xs text-gray-500 mt-1">You can use merge tags like {{first_name}} for personalization</p>
          </div>

          <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <div class="flex space-x-3">
              <%= link_to "Cancel", campaign_path(@campaign), 
                  class: "btn-secondary" %>
            </div>
            <div class="flex space-x-3">
              <%= form.submit "Save Draft", 
                  class: "btn-secondary" %>
              <%= form.submit "Create Email Content", 
                  class: "btn-primary" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- AI Content Generation Sidebar -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">AI Content Generation</h3>
        <p class="text-sm text-gray-600 mb-4">Let AI help you create compelling email content based on your campaign details.</p>
        
        <%= form_with url: generate_ai_content_campaign_email_content_path(@campaign), 
            method: :post, local: true, class: "space-y-4" do |form| %>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= form.label :brand_voice, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.select :brand_voice, 
                  options_for_select([
                    ['Professional', 'professional'],
                    ['Friendly', 'friendly'],
                    ['Casual', 'casual'],
                    ['Authoritative', 'authoritative'],
                    ['Playful', 'playful']
                  ], 'professional'),
                  {}, { class: "form-select" } %>
            </div>

            <div>
              <%= form.label :email_type, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.select :email_type,
                  options_for_select([
                    ['Promotional', 'promotional'],
                    ['Newsletter', 'newsletter'],
                    ['Welcome', 'welcome'],
                    ['Follow-up', 'followup'],
                    ['Announcement', 'announcement']
                  ], 'promotional'),
                  {}, { class: "form-select" } %>
            </div>
          </div>

          <div>
            <%= form.label :key_message, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :key_message, 
                placeholder: "What's the main message you want to convey?",
                class: "form-textarea", rows: 3 %>
          </div>

          <%= form.submit "Generate AI Content", 
              class: "w-full btn-primary",
              data: { turbo_confirm: "This will populate the form with AI-generated content. Continue?" } %>
        <% end %>
      </div>
    </div>
  </div>
</div>
