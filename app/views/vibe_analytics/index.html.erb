<% content_for :title, "Vibe Analytics" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Vibe Analytics</h1>
          <p class="mt-1 text-sm text-gray-600">Monitor emotional resonance and cultural alignment across your campaigns</p>
        </div>
        
        <!-- Filters -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <label for="date_range" class="text-sm font-medium text-gray-700">Period:</label>
            <select id="date_range" name="date_range" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
              <option value="7_days" <%= 'selected' if @date_range == '7_days' %>>Last 7 days</option>
              <option value="30_days" <%= 'selected' if @date_range == '30_days' %>>Last 30 days</option>
              <option value="90_days" <%= 'selected' if @date_range == '90_days' %>>Last 90 days</option>
              <option value="1_year" <%= 'selected' if @date_range == '1_year' %>>Last year</option>
            </select>
          </div>
          
          <div class="flex items-center space-x-2">
            <label for="campaign_filter" class="text-sm font-medium text-gray-700">Campaigns:</label>
            <select id="campaign_filter" name="campaign_filter" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm">
              <option value="all" <%= 'selected' if @campaign_filter == 'all' %>>All campaigns</option>
              <option value="active" <%= 'selected' if @campaign_filter == 'active' %>>Active only</option>
              <option value="completed" <%= 'selected' if @campaign_filter == 'completed' %>>Completed only</option>
              <option value="vibe_approved" <%= 'selected' if @campaign_filter == 'vibe_approved' %>>Vibe approved</option>
              <option value="vibe_flagged" <%= 'selected' if @campaign_filter == 'vibe_flagged' %>>Vibe flagged</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Overview Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Campaigns -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Campaigns</p>
            <p class="text-2xl font-bold text-gray-900" data-metric="total_campaigns"><%= @analytics_data[:overview][:total_campaigns] %></p>
          </div>
        </div>
      </div>

      <!-- Approval Rate -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Vibe Approval Rate</p>
            <p class="text-2xl font-bold text-gray-900" data-metric="approval_rate"><%= @analytics_data[:overview][:approval_rate] %>%</p>
          </div>
        </div>
      </div>

      <!-- Average Cultural Score -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Avg Cultural Score</p>
            <p class="text-2xl font-bold text-gray-900" data-metric="avg_cultural_score"><%= @analytics_data[:overview][:avg_cultural_score] %></p>
          </div>
        </div>
      </div>

      <!-- Average Authenticity Score -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Avg Authenticity Score</p>
            <p class="text-2xl font-bold text-gray-900" data-metric="avg_authenticity_score"><%= @analytics_data[:overview][:avg_authenticity_score] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Emotional Tone Distribution -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Emotional Tone Distribution</h3>
          <div class="text-sm text-gray-500">
            Total: <%= @analytics_data[:emotional_trends][:total_analyzed] %> campaigns
          </div>
        </div>
        <div id="emotional-tone-chart" class="h-64"></div>
      </div>

      <!-- Cultural Relevance Scores -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Cultural Relevance Scores</h3>
          <div class="text-sm text-gray-500">
            Avg: <%= @analytics_data[:cultural_metrics][:average_score] %>
          </div>
        </div>
        <div id="cultural-scores-chart" class="h-64"></div>
      </div>
    </div>

    <!-- Second Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Authenticity Distribution -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Authenticity Distribution</h3>
          <div class="text-sm text-gray-500">
            Verification Rate: <%= @analytics_data[:authenticity_stats][:verification_rate] %>%
          </div>
        </div>
        <div id="authenticity-chart" class="h-64"></div>
      </div>

      <!-- Vibe Status Overview -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Vibe Status Overview</h3>
        </div>
        <div id="vibe-status-chart" class="h-64"></div>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
      <div class="px-6 py-4 border-b border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900">Recent Vibe Analysis Activities</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vibe Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emotional Tone</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cultural Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authenticity Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @analytics_data[:recent_activities].each do |activity| %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">
                    <%= link_to activity[:name], campaign_path(activity[:id]), class: "text-blue-600 hover:text-blue-800" %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                    <%= case activity[:vibe_status]
                        when 'vibe_approved' then 'bg-green-100 text-green-800'
                        when 'vibe_flagged' then 'bg-red-100 text-red-800'
                        when 'analyzing' then 'bg-yellow-100 text-yellow-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= activity[:vibe_status]&.humanize || 'Pending' %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= activity[:emotional_tone]&.humanize || '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= activity[:cultural_score] || '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= activity[:authenticity_score] || '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= time_ago_in_words(activity[:updated_at]) %> ago
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Chart Data -->
<script>
  window.vibeAnalyticsData = <%= raw @analytics_data.to_json %>;
</script>
