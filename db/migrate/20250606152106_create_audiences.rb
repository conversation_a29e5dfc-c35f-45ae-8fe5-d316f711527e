class CreateAudiences < ActiveRecord::Migration[8.0]
  def change
    create_table :audiences do |t|
      t.string :name
      t.text :description
      t.text :target_demographics
      t.string :cultural_context
      t.string :primary_language
      t.string :secondary_languages
      t.string :geographic_regions
      t.integer :age_range_min
      t.integer :age_range_max
      t.text :interests
      t.text :behavioral_traits
      t.text :communication_preferences
      t.text :cultural_values
      t.text :engagement_patterns
      t.decimal :cultural_alignment_score
      t.jsonb :demographics
      t.jsonb :cultural_attributes
      t.jsonb :preferences
      t.references :tenant, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: true

      t.timestamps
    end
  end
end
